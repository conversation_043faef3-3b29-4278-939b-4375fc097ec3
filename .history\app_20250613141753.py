from flask import Flask, request, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from functools import wraps
from werkzeug.utils import timedelta

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 添加密钥用于session加密
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:root@localhost/medical'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)

db = SQLAlchemy(app)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)  # 修改字段名
    phone = db.Column(db.String(20), unique=True)
    name = db.Column(db.String(10), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime)

    def set_password(self, password):
        if len(password) < 8:
            raise ValueError("密码至少需要8个字符")
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return '<User %r>' % self.username

class MedicalIndex(db.Model):
    index_id = db.Column(db.Integer, primary_key=True)
    index_name = db.Column(db.String(100), nullable=False)
    index_unit = db.Column(db.String(60))
    reference_min = db.Column(db.Float)
    reference_max = db.Column(db.Float)
    index_type = db.Column(db.String(1))
    is_chart = db.Column(db.String(1))

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    # 添加参数验证
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({'status': 'error', 'message': 'Missing username or password'}), 400
    
    user = User.query.filter_by(username=data['username']).first()
    if user and user.check_password(data['password']):  # 使用密码哈希验证
        session['user_id'] = user.id  # 添加session设置
        return jsonify({'status': 'success', 'user_id': user.id})
    else:
        return jsonify({'status': 'error', 'message': 'Invalid credentials'}), 401

def login_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Unauthorized'}), 401
        return f(*args, **kwargs)
    return decorated

@app.route('/user/<int:user_id>', methods=['GET'])
@login_required
def get_user(user_id):
    user = User.query.get(user_id)
    if user:
        return jsonify({'username': user.username, 'phone': user.phone})  # 修复转义字符
    else:
        return jsonify({'status': 'error', 'message': 'User not found'}), 404

@app.route('/medical_index', methods=['GET'])
@login_required
def get_medical_index():
    index = MedicalIndex.query.all()
    return jsonify([{
        'index_id': idx.index_id,
        'index_name': idx.index_name,
        'index_unit': idx.index_unit,
        'reference_min': idx.reference_min,
        'reference_max': idx.reference_max,
        'index_type': idx.index_type,
        'is_chart': idx.is_chart
    } for idx in index])

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)