from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
import logging
from .config import Config

# 初始化扩展，但不绑定app
db = SQLAlchemy()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化日志
    logging.basicConfig(level=logging.DEBUG)
    
    # 初始化扩展
    db.init_app(app)
    CORS(app, origins=app.config['CORS_ORIGINS'])
    
    # 注册蓝图
    from app.api import auth_bp, user_bp, medical_bp
    app.register_blueprint(auth_bp)  # 不添加前缀，保持与原app.py一致
    app.register_blueprint(user_bp)  # 不添加前缀，保持与原app.py一致
    app.register_blueprint(medical_bp)  # 不添加前缀，保持与原app.py一致
    
    @app.route('/health')
    def health_check():
        return {'status': 'healthy'}
    
    return app
