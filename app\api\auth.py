from flask import request, jsonify, session
import logging
from app.models import User
from app.api import auth_bp
from app.utils.auth import generate_token

logger = logging.getLogger(__name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    logger.info('收到登录请求，客户端IP：%s', request.remote_addr)
    data = request.get_json()
    logger.debug('请求参数：%s', data)
    
    if not data or 'username' not in data or 'password' not in data:
        logger.warning('缺少必要参数，请求数据：%s', data)
        return jsonify({'status': 'error', 'message': 'Missing username or password'}), 400
    
    try:
        user = User.query.filter_by(username=data['username']).first()
        if user and user.check_password(data['password']):
            logger.info('用户 %s 登录成功', data['username'])
            session['user_id'] = user.user_id
            token = generate_token(user.user_id)
            return jsonify({
                'status': 'success', 
                'token': token, 
                'userInfo': {
                    'user_id': user.user_id, 
                    'name': user.name, 
                    'phone': user.phone
                }
            })
        else:
            logger.warning('登录失败，用户名：%s', data['username'])
            return jsonify({'status': 'error', 'message': '登录失败！'})
    except Exception as e:
        logger.error('登录过程中发生异常：%s', str(e))
        return jsonify({'status': 'error', 'message': '系统错误！'}), 500

