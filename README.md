# 医疗数据管理系统

这是一个用于管理个人医疗数据的Flask应用。

## 功能特点

- 用户认证与授权
- 医疗指标管理
- 体检记录管理
- 体检明细数据管理
- 数据可视化支持

## 技术栈

- 后端: Flask, SQLAlchemy
- 数据库: MySQL
- 认证: JWT

## 安装与运行

1. 克隆仓库
```bash
git clone <repository-url>
cd medical_app
```

2. 创建并激活虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate  # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，设置数据库连接等配置
```

5. 初始化数据库
```bash
flask --app run.py db init
flask --app run.py db migrate
flask --app run.py db upgrade
```

6. 运行应用
```bash
python run.py
```
或
```bash
flask --app run.py run --debug
```

## API文档

### 认证API
- POST /auth/login - 用户登录

### 用户API
- GET /user/<user_id> - 获取用户信息
- POST /user/update - 更新用户信息
- GET /user/index - 获取用户关联的指标
- POST /user/index - 添加用户指标关联
- DELETE /user/index/<index_id> - 删除用户指标关联

### 医疗API
- GET /medical/index - 获取所有医疗指标
- GET /medical/type - 获取所有医疗类型
- POST /medical/check/query - 分页查询体检记录
- POST /medical/check/detail/query - 查询体检明细
- POST /medical/check - 添加体检记录
- PUT /medical/check/<medical_id> - 更新体检记录
- DELETE /medical/check/<medical_id> - 删除体检记录
- POST /medical/check/detail - 添加体检明细
- PUT /medical/check/detail/<detail_id> - 更新体检明细
- DELETE /medical/check/detail/<detail_id> - 删除体检明细
- GET /medical/value - 获取指标历史数据

## 许可证

[MIT](LICENSE)