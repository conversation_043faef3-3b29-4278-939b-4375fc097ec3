from flask import request, jsonify
import logging
from app import db
from app.models import User
from app.api import user_bp
from app.utils.auth import token_required

logger = logging.getLogger(__name__)

@user_bp.route('/user/<int:user_id>', methods=['GET'])
@token_required
def get_user(current_user, user_id):
    user = User.query.get(user_id)
    if user:
        return jsonify({'username': user.username, 'phone': user.phone})
    else:
        return jsonify({'status': 'error', 'message': 'User not found'}), 404

@user_bp.route('/update', methods=['POST'])
@token_required
def update_user(current_user):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
            
        user = User.query.get(current_user.user_id)
        if not user:
            return jsonify({'status': 'error', 'message': '用户不存在'}), 404
            
        if 'phone' in data:
            user.phone = data['phone']
        if 'name' in data:
            user.name = data['name']
            
        db.session.commit()
        return jsonify({'status': 'success', 'message': '更新成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('更新用户信息失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'更新失败: {str(e)}'}), 500

@user_bp.route('/index', methods=['GET'])
@token_required
def get_user_indices(current_user):
    try:
        user_indices = UserIndex.query.filter_by(user_id=current_user.user_id).order_by(UserIndex.sort).all()
        
        result = []
        for ui in user_indices:
            index = MedicalIndex.query.get(ui.index_id)
            if index:
                result.append({
                    'index_id': index.index_id,
                    'index_name': index.index_name,
                    'index_unit': index.index_unit,
                    'reference_min': index.reference_min,
                    'reference_max': index.reference_max,
                    'medical_type': index.medical_type,
                    'is_chart': index.is_chart,
                    'description': index.description,
                    'sort': ui.sort
                })
                
        return jsonify(result)
    except Exception as e:
        logger.error('获取用户指标失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'查询失败: {str(e)}'}), 500

@user_bp.route('/index', methods=['POST'])
@token_required
def add_user_index(current_user):
    try:
        data = request.get_json()
        if not data or 'index_id' not in data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
            
        # 检查指标是否存在
        index = MedicalIndex.query.get(data['index_id'])
        if not index:
            return jsonify({'status': 'error', 'message': '指标不存在'}), 404
            
        # 检查是否已经关联
        existing = UserIndex.query.filter_by(
            user_id=current_user.user_id, 
            index_id=data['index_id']
        ).first()
        
        if existing:
            return jsonify({'status': 'error', 'message': '已关联该指标'}), 400
            
        # 获取最大排序值
        max_sort = db.session.query(db.func.max(UserIndex.sort)).filter(
            UserIndex.user_id == current_user.user_id
        ).scalar() or 0
        
        # 创建新关联
        new_user_index = UserIndex(
            user_id=current_user.user_id,
            index_id=data['index_id'],
            sort=max_sort + 1
        )
        
        db.session.add(new_user_index)
        db.session.commit()
        
        return jsonify({'status': 'success', 'message': '添加成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('添加用户指标关联失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'添加失败: {str(e)}'}), 500

@user_bp.route('/index/<int:index_id>', methods=['DELETE'])
@token_required
def delete_user_index(current_user, index_id):
    try:
        user_index = UserIndex.query.filter_by(
            user_id=current_user.user_id,
            index_id=index_id
        ).first()
        
        if not user_index:
            return jsonify({'status': 'error', 'message': '关联不存在'}), 404
            
        db.session.delete(user_index)
        db.session.commit()
        
        return jsonify({'status': 'success', 'message': '删除成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('删除用户指标关联失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'删除失败: {str(e)}'}), 500


