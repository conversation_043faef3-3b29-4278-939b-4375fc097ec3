from flask import Flask, request, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
from datetime import datetime, timedelta
from functools import wraps
import secrets
import logging
from flask_cors import CORS

# 初始化日志配置
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app, origins=['https://znick.tpddns.cn:60012', 'http://localhost:5173'])

app.secret_key = secrets.token_hex(32)  # 添加密钥用于session加密
app.config['JWT_SECRET_KEY'] = secrets.token_hex(32)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:root@localhost/medical'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=1)
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
app.config['SQLALCHEMY_POOL_RECYCLE'] = 2800  # 小于MySQL的wait_timeout(默认28800秒)
app.config['SQLALCHEMY_POOL_TIMEOUT'] = 15  # 连接超时时间
app.config['SQLALCHEMY_POOL_PRE_PING'] = True  # 执行前检查连接有效性

db = SQLAlchemy(app)

class User(db.Model):
    user_id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)  # 修改字段名
    phone = db.Column(db.String(20), unique=True)
    name = db.Column(db.String(10), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime)

    def set_password(self, password):
        if len(password) < 8:
            raise ValueError("密码至少需要8个字符")
        self.password = generate_password_hash(password, method='scrypt')

    def check_password(self, password):
        return self.password == password

    def __repr__(self):
        return '<User %r>' % self.username

class MedicalIndex(db.Model):
    index_id = db.Column(db.Integer, primary_key=True)
    index_name = db.Column(db.String(100), nullable=False)
    index_unit = db.Column(db.String(60))
    reference_min = db.Column(db.Float)
    reference_max = db.Column(db.Float)
    index_type = db.Column(db.String(1))
    is_chart = db.Column(db.String(1))

class MedicalCheck(db.Model):
    medical_id = db.Column(db.Integer, primary_key=True)
    medical_date = db.Column(db.DateTime)
    user_id = db.Column(db.Integer, db.ForeignKey('user.user_id'))
    hospital = db.Column(db.String(100))
    medical_type = db.Column(db.String(60))
    status = db.Column(db.String(20))

class MedicalCheckDetail(db.Model):
    medical_detail_id = db.Column(db.Integer, primary_key=True)
    medical_id = db.Column(db.Integer, db.ForeignKey('medical_check.medical_id'))
    index_name = db.Column(db.String(100), nullable=False)
    index_value = db.Column(db.String(60))
    index_unit = db.Column(db.String(60))
    reference_value = db.Column(db.String(100))
    index_status = db.Column(db.String(10))
    check = db.relationship('MedicalCheck', backref=db.backref('details', lazy=True))

def generate_token(user_id):
    payload = {
        'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES'],
        'iat': datetime.utcnow(),
        'sub': user_id
    }
    return jwt.encode(payload, app.config['JWT_SECRET_KEY'], algorithm='HS256')

@app.route('/login', methods=['POST'])
def login():
    logger.info('收到登录请求，客户端IP：%s', request.remote_addr)
    data = request.get_json()
    logger.debug('请求参数：%s', data)
    # 添加参数验证
    if not data or 'username' not in data or 'password' not in data:
        logger.warning('缺少必要参数，请求数据：%s', data)
        return jsonify({'status': 'error', 'message': 'Missing username or password'}), 400
    
    try:
        user = User.query.filter_by(username=data['username']).first()
        if user and user.check_password(data['password']):  # 使用密码哈希验证
            logger.info('用户 %s 登录成功', data['username'])
            session['user_id'] = user.user_id  # 添加session设置
            token = generate_token(user.user_id)
            return jsonify({'status': 'success', 'token': token, 'userInfo': {'user_id': user.user_id, 'name': user.name, 'phone': user.phone}})
        else:
            logger.warning('登录失败，用户名：%s', data['username'])
            return jsonify({'status': 'error', 'message': '登录失败！'})
    except Exception as e:
        logger.error('登录过程中发生异常：%s', str(e))
        return jsonify({'status': 'error', 'message': '系统错误！'}), 500

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'status': 'error', 'message': 'Token is missing'}), 401

        try:
            token = token.split(" ")[1]  # 去除Bearer前缀
            data = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            current_user = User.query.get(data['sub'])
        except jwt.ExpiredSignatureError:
            return jsonify({'status': 'error', 'message': 'Token expired'}), 401
        except (jwt.InvalidTokenError, Exception) as e:
            return jsonify({'status': 'error', 'message': 'Invalid token'}), 401

        return f(current_user, *args, **kwargs)
    return decorated

@app.route('/user/<int:user_id>', methods=['GET'])
@token_required
def get_user(user_id):
    user = User.query.get(user_id)
    if user:
        return jsonify({'username': user.username, 'phone': user.phone})  # 修复转义字符
    else:
        return jsonify({'status': 'error', 'message': 'User not found'}), 404

@app.route('/medical_index', methods=['GET'])
@token_required
def get_medical_index():
    index = MedicalIndex.query.all()
    return jsonify([{
        'index_id': idx.index_id,
        'index_name': idx.index_name,
        'index_unit': idx.index_unit,
        'reference_min': idx.reference_min,
        'reference_max': idx.reference_max,
        'index_type': idx.index_type,
        'is_chart': idx.is_chart
    } for idx in index])

@app.route('/medical_check', methods=['GET'])
@token_required
def get_medical_check(current_user):
    try:
        page = request.args.get('page', default=1, type=int)
        page_size = request.args.get('pageSize', default=10, type=int)

        # 修改后的分页逻辑
        base_query = MedicalCheck.query.order_by(MedicalCheck.medical_date.desc())
        
        # 手动计算总数
        total = base_query.count()
        
        # 使用slice分页
        items = base_query.slice((page-1)*page_size, page*page_size).all()
        
        check_list = [{
            'medical_id': chk.medical_id,
            'user_id': chk.user_id,
            'hospital': chk.hospital,
            'medical_date': chk.medical_date.strftime('%Y-%m-%d'),  # 新增格式化
            'medical_type': chk.medical_type,
            'status': chk.status
        } for chk in items]

        return jsonify({
            'data': check_list,
            'pagination': {
                'total': (total + page_size - 1) // page_size,
                'page': page,
                'pageSize': page_size
            }
        })
    except Exception as e:
        logger.error('分页查询失败: %s', str(e))
        return jsonify({'status': 'error', 'message': '查询失败'}), 500

@app.route('/medical_check_detail', methods=['POST'])
@token_required
def get_medical_check_detail(current_user):  # 添加current_user参数
    data = request.get_json()
    if not data or 'medical_id' not in data:
        return jsonify({'status': 'error', 'message': 'Missing medical_id'}), 400
    check = MedicalCheckDetail.query.filter_by(medical_id=data['medical_id']).all()
    return jsonify({'results': [{
        'medical_detail_id': chk.medical_detail_id,
        'medical_id': chk.medical_id,
        'index_name': chk.index_name,
        'index_value': chk.index_value,
        'index_unit': chk.index_unit,
        'reference_value': chk.reference_value,
        'index_status': chk.index_status
    } for chk in check]})

@app.route('/get_value', methods=['GET'])
@token_required
def get_value():
    index_name = request.args.get('index_name')
    if not index_name:
        return jsonify({'error': '缺少参数'}), 400

    from sqlalchemy.sql import text
    query = text('''
        SELECT c.medical_date, t.* 
        FROM medical_check_detail t
        INNER JOIN medical_check c ON t.medical_id = c.medical_id
        WHERE t.index_name = :index_name
        ORDER BY c.medical_date
    ''')
    
    try:
        result = db.session.execute(query, {'index_name': index_name})
        data = [dict(row) for row in result.mappings()]
        return jsonify({'data': data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)