import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化日期时间"""
    if isinstance(dt, str):
        try:
            dt = datetime.strptime(dt, '%Y-%m-%d')
        except ValueError:
            try:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                logger.error(f"无法解析日期时间字符串: {dt}")
                return None
    
    if isinstance(dt, datetime):
        return dt.strftime(format_str)
    
    return None

def parse_date(date_str, default=None):
    """解析日期字符串为datetime对象"""
    if not date_str:
        return default
        
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            logger.error(f"无法解析日期字符串: {date_str}")
            return default