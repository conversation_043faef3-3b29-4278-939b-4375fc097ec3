from flask import request, jsonify
import logging
from datetime import datetime
from app import db
from app.models import (
    MedicalIndex, MedicalCheck, MedicalCheckDetail, 
    MedicalType, UserIndex, User, MedicalExam, ExamType
)
from app.api import medical_bp
from app.utils.auth import token_required
from sqlalchemy.sql import text

logger = logging.getLogger(__name__)

@medical_bp.route('/medical_index/query', methods=['POST'])
@token_required
def get_medical_index(current_user):
    # 从查询参数获取medical_type和user_id
    medical_type = request.args.get('medical_type')
    user_id = request.args.get('user_id')
    
    # 添加参数验证
    if not medical_type:
        return jsonify({'status': 'error', 'message': 'Missing medical_type parameter'}), 400
    
    # 通过user_id关联user_index表查询，并按user_index表中的排序顺序重新排列结果
    if int(medical_type) == 1 and user_id:
        # 先从user_index表获取该用户的index_id列表
        user_indices = UserIndex.query.filter_by(user_id=user_id).order_by(UserIndex.sort).all()
        index_ids = [ui.index_id for ui in user_indices]
        
        # 然后从medical_index表查询这些index_id对应的记录
        if index_ids:
            indices = MedicalIndex.query.filter(MedicalIndex.index_id.in_(index_ids)).all()
            sorted_indices = []
            for idx_id in index_ids:
                for idx in indices:
                    if idx.index_id == idx_id:
                        sorted_indices.append(idx)
                        break
            indices = sorted_indices
        else:
            indices = []
    else:
        # 按类型过滤并排序
        indices = MedicalIndex.query.filter_by(medical_type=medical_type).order_by(MedicalIndex.sort).all()
    
    # 格式化返回结果
    return jsonify([{
        'index_id': idx.index_id,
        'index_name': idx.index_name,
        'index_unit': idx.index_unit,
        'reference_min': idx.reference_min,
        'reference_max': idx.reference_max,
        'medical_type': idx.medical_type,
        'is_chart': idx.is_chart,
        'description': idx.description,
        'is_edit': idx.is_edit
    } for idx in indices])

@medical_bp.route('/medical_check/query', methods=['POST'])
@token_required
def get_medical_check(current_user):
    try:
        data = request.get_json() or {}
        page = data.get('page', 1)
        page_size = data.get('pageSize', 10)

        # 修改后的分页逻辑
        base_query = MedicalCheck.query.filter(MedicalCheck.medical_type!=3).order_by(MedicalCheck.medical_date.desc())
        
        # 手动计算总数
        total = base_query.count()
        
        # 使用slice分页
        items = base_query.slice((page-1)*page_size, page*page_size).all()
        
        check_list = []
        for chk in items:
            # 获取关联的医疗类型名称
            type_name = None
            if chk.medical_type:
                type_obj = MedicalType.query.get(chk.medical_type)
                if type_obj:
                    type_name = type_obj.type_name
            
            check_list.append({
                'medical_id': chk.medical_id,
                'user_id': chk.user_id,
                'hospital': chk.hospital,
                'medical_date': chk.medical_date.strftime('%Y-%m-%d'),
                'medical_type': type_name,  # 添加类型名称
                'comment': chk.comment,
                'status': chk.status
            })

        return jsonify({
            'data': check_list,
            'pagination': {
                'total': (total + page_size - 1) // page_size,
                'page': page,
                'pageSize': page_size
            }
        })
    except Exception as e:
        logger.error('分页查询失败: %s', str(e))
        return jsonify({'status': 'error', 'message': '查询失败'}), 500

@medical_bp.route('/medical_check_detail/query', methods=['POST'])
@token_required
def get_medical_check_detail(current_user):
    data = request.get_json()
    if not data or 'medical_id' not in data:
        return jsonify({'status': 'error', 'message': 'Missing medical_id'}), 400
    check = MedicalCheckDetail.query.filter_by(medical_id=data['medical_id']).all()
    return jsonify({'results': [{
        'medical_detail_id': chk.medical_detail_id,
        'medical_id': chk.medical_id,
        'index_name': chk.index_name,
        'index_value': chk.index_value,
        'index_unit': chk.index_unit,
        'reference_value': chk.reference_value,
        'index_status': chk.index_status
    } for chk in check]})

@medical_bp.route('/medical_index_detail/query', methods=['POST'])
@token_required
def medical_index_detail(current_user):
    data = request.get_json()
    if not data or 'index_name' not in data:
        return jsonify({'error': '缺少参数'}), 400

    from sqlalchemy.sql import text
    query = text('''
        SELECT c.medical_date, c.comment, t.* 
        FROM medical_check_detail t
        INNER JOIN medical_check c ON t.medical_id = c.medical_id
        WHERE t.index_name = :index_name
        ORDER BY c.medical_date
    ''')
    
    try:
        result = db.session.execute(query, {'index_name': data['index_name']})
        # 格式化medical_date为YYYY-MM-DD格式
        formatted_data = []
        for row in result.mappings():
            row_dict = dict(row)
            # 检查并格式化日期字段
            if 'medical_date' in row_dict and row_dict['medical_date']:
                row_dict['medical_date'] = row_dict['medical_date'].strftime('%Y-%m-%d')
            formatted_data.append(row_dict)
        return jsonify({'data': formatted_data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@medical_bp.route('/medical_type/query', methods=['POST'])
@token_required
def get_medical_type(current_user):
    try:
        # 查询所有医疗类型并按sort字段排序
        types = MedicalType.query.order_by(MedicalType.sort).all()
        
        # 格式化返回数据
        type_list = [{
            'type_id': t.type_id,
            'type_name': t.type_name,
            'sort': t.sort
        } for t in types]
        
        return jsonify({
            'status': 'success',
            'data': type_list
        })
    except Exception as e:
        logger.error('查询医疗类型失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'查询失败: {str(e)}'}), 500

# 添加 medical_check 的增加接口
@medical_bp.route('/medical_check', methods=['POST'])
@token_required
def add_medical_check(current_user):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
        
        # 创建新的体检记录
        new_check = MedicalCheck(
            medical_date=datetime.strptime(data.get('medical_date'), '%Y-%m-%d') if data.get('medical_date') else datetime.now(),
            user_id=current_user.user_id,
            hospital=data.get('hospital'),
            medical_type=data.get('medical_type'),
            comment=data.get('comment'),
            status=data.get('status', 'normal')
        )
        
        db.session.add(new_check)
        db.session.commit()
        
        return jsonify({
            'status': 'success', 
            'message': '添加成功',
            'medical_id': new_check.medical_id
        })
    except Exception as e:
        db.session.rollback()
        logger.error('添加体检记录失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'添加失败: {str(e)}'}), 500

# 添加 medical_check 的修改接口
@medical_bp.route('/medical_check/<int:medical_id>', methods=['PUT'])
@token_required
def update_medical_check(current_user, medical_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
        
        check = MedicalCheck.query.get(medical_id)
        if not check:
            return jsonify({'status': 'error', 'message': '记录不存在'}), 404
        
        # 更新体检记录
        if 'medical_date' in data:
            check.medical_date = datetime.strptime(data['medical_date'], '%Y-%m-%d')
        if 'hospital' in data:
            check.hospital = data['hospital']
        if 'medical_type' in data:
            check.medical_type = data['medical_type']
        if 'comment' in data:
            check.comment = data['comment']
        if 'status' in data:
            check.status = data['status']
        
        db.session.commit()
        return jsonify({'status': 'success', 'message': '更新成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('更新体检记录失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'更新失败: {str(e)}'}), 500

# 添加 medical_check 的删除接口
@medical_bp.route('/medical_check/<int:medical_id>', methods=['DELETE'])
@token_required
def delete_medical_check(current_user, medical_id):
    try:
        check = MedicalCheck.query.get(medical_id)
        if not check:
            return jsonify({'status': 'error', 'message': '记录不存在'}), 404
        
        # 删除体检记录 (关联的明细会通过级联删除自动删除)
        db.session.delete(check)
        db.session.commit()
        return jsonify({'status': 'success', 'message': '删除成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('删除体检记录失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'删除失败: {str(e)}'}), 500

# 添加 medical_check_detail 的修改接口
@medical_bp.route('/medical_check_detail/<int:detail_id>', methods=['PUT'])
@token_required
def update_medical_check_detail(current_user, detail_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
        
        detail = MedicalCheckDetail.query.get(detail_id)
        if not detail:
            return jsonify({'status': 'error', 'message': '记录不存在'}), 404
        
        # 更新体检明细
        if 'index_name' in data:
            detail.index_name = data['index_name']
        if 'index_value' in data:
            detail.index_value = data['index_value']
        if 'index_unit' in data:
            detail.index_unit = data['index_unit']
        if 'reference_value' in data:
            detail.reference_value = data['reference_value']
        if 'index_status' in data:
            detail.index_status = data['index_status']
        
        db.session.commit()
        return jsonify({'status': 'success', 'message': '更新成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('更新体检明细失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'更新失败: {str(e)}'}), 500

# 添加 medical_check_detail 的删除接口
@medical_bp.route('/medical_check_detail/<int:detail_id>', methods=['DELETE'])
@token_required
def delete_medical_check_detail(current_user, detail_id):
    try:
        detail = MedicalCheckDetail.query.get(detail_id)
        if not detail:
            return jsonify({'status': 'error', 'message': '记录不存在'}), 404
        
        # 删除体检明细
        db.session.delete(detail)
        db.session.commit()
        return jsonify({'status': 'success', 'message': '删除成功'})
    except Exception as e:
        db.session.rollback()
        logger.error('删除体检明细失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'删除失败: {str(e)}'}), 500

# 添加用户指标关联接口
@medical_bp.route('/user_index', methods=['POST'])
@token_required
def add_user_index(current_user):
    try:
        data = request.get_json()
        if not data or 'user_id' not in data or 'index_id' not in data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
        
        user_id = data['user_id']
        index_id = data['index_id']
        
        # 检查用户是否存在
        user = User.query.get(user_id)
        if not user:
            return jsonify({'status': 'error', 'message': '用户不存在'}), 404
        
        # 检查指标是否存在
        index = MedicalIndex.query.get(index_id)
        if not index:
            return jsonify({'status': 'error', 'message': '指标不存在'}), 404
        
        # 检查关联是否已存在
        existing = UserIndex.query.filter_by(user_id=user_id, index_id=index_id).first()
        if existing:
            return jsonify({'status': 'error', 'message': '关联已存在'}), 400
        
        # 获取当前最大排序值
        max_sort = db.session.query(db.func.max(UserIndex.sort)).filter_by(user_id=user_id).scalar() or 0
        
        # 创建新的关联
        new_user_index = UserIndex(
            user_id=user_id,
            index_id=index_id,
            sort=max_sort + 1  # 新关联的排序值为当前最大值+1
        )
        
        db.session.add(new_user_index)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '添加成功',
            'data': {
                'user_id': new_user_index.user_id,
                'index_id': new_user_index.index_id,
                'sort': new_user_index.sort
            }
        })
    except Exception as e:
        db.session.rollback()
        logger.error('添加用户指标关联失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'添加失败: {str(e)}'}), 500

# 删除用户指标关联接口
@medical_bp.route('/user_index', methods=['DELETE'])
@token_required
def delete_user_index(current_user):
    try:
        data = request.get_json()
        if not data or 'user_id' not in data or 'index_id' not in data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
        
        user_id = data['user_id']
        index_id = data['index_id']
        
        # 查找关联记录
        user_index = UserIndex.query.filter_by(user_id=user_id, index_id=index_id).first()
        if not user_index:
            return jsonify({'status': 'error', 'message': '关联不存在'}), 404
        
        # 删除关联
        db.session.delete(user_index)
        
        # 重新排序剩余关联
        remaining = UserIndex.query.filter_by(user_id=user_id).order_by(UserIndex.sort).all()
        for i, item in enumerate(remaining, 1):
            item.sort = i
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '删除成功'
        })
    except Exception as e:
        db.session.rollback()
        logger.error('删除用户指标关联失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'删除失败: {str(e)}'}), 500

@medical_bp.route('/medical_check_detail', methods=['POST'])
@token_required
def add_medical_check_detail(current_user):
    try:
        data = request.get_json()
        if not data or 'index_name' not in data or 'medical_date' not in data:
            return jsonify({'status': 'error', 'message': '缺少必要参数'}), 400
        
        # 检查是否已存在相同日期、用户和类型的体检记录
        medical_date = datetime.strptime(data.get('medical_date'), '%Y-%m-%d') if data.get('medical_date') else datetime.now()
        user_id = current_user.user_id
        medical_type = data.get('medical_type')
        
        existing_check = MedicalCheck.query.filter_by(
            medical_date=medical_date,
            user_id=user_id,
            medical_type=medical_type
        ).first()
        
        if existing_check:
            # 使用已存在的记录
            medical_id = existing_check.medical_id
        else:
            # 创建新的体检记录
            new_check = MedicalCheck(
                medical_date=medical_date,
                user_id=user_id,
                hospital=data.get('hospital'),
                medical_type=medical_type,
                comment=data.get('comment'),
                status=data.get('status', 'normal')
            )
            
            db.session.add(new_check)
            db.session.flush()  # 获取新生成的ID但不提交事务
            medical_id = new_check.medical_id
        
        # 检查是否已存在相同medical_id和index_name的明细
        existing_detail = MedicalCheckDetail.query.filter_by(
            medical_id=medical_id,
            index_name=data.get('index_name')
        ).first()
        
        if existing_detail:
            db.session.rollback()
            return jsonify({
                'status': 'error', 
                'message': f'该体检记录中已存在指标 {data.get('index_name')}'
            }), 400
        
        # 创建新的体检明细
        new_detail = MedicalCheckDetail(
            medical_id=medical_id,
            index_name=data.get('index_name'),
            index_value=data.get('index_value'),
            index_unit=data.get('index_unit'),
            reference_value=data.get('reference_value'),
            index_status=data.get('index_status', 'normal')
        )
        
        db.session.add(new_detail)
        db.session.commit()
        
        return jsonify({
            'status': 'success', 
            'message': '添加成功',
            'data': {
                'medical_id': medical_id,
                'medical_detail_id': new_detail.medical_detail_id
            }
        })
    except Exception as e:
        db.session.rollback()
        logger.error('添加体检记录及明细失败: %s', str(e))
        return jsonify({'status': 'error', 'message': f'添加失败: {str(e)}'}), 500

@medical_bp.route('/medical_exam/query', methods=['POST'])
@token_required
def get_medical_exam(current_user):
    try:
        data = request.get_json() or {}
        page = data.get('page', 1)
        page_size = data.get('pageSize', 10)

        # 修改后的分页逻辑
        base_query = MedicalExam.query.order_by(MedicalExam.medical_date.desc())
        
        # 手动计算总数
        total = base_query.count()
        
        # 使用slice分页
        items = base_query.slice((page-1)*page_size, page*page_size).all()
        
        check_list = []
        for chk in items:
            # 获取关联的医疗类型名称
            type_name = None
            if chk.exam_type:
                type_obj = ExamType.query.get(chk.exam_type)
                if type_obj:
                    type_name = type_obj.type_name
            
            check_list.append({
                'exam_id': chk.exam_id,
                'user_id': chk.user_id,
                'hospital': chk.hospital,
                'medical_date': chk.medical_date.strftime('%Y-%m-%d'),
                'exam_type': type_name,  # 添加类型名称
                'exam_info': chk.exam_info,
                'exam_diag': chk.exam_diag,
                'comment': chk.comment,
                'status': chk.status
            })

        return jsonify({
            'data': check_list,
            'pagination': {
                'total': (total + page_size - 1) // page_size,
                'page': page,
                'pageSize': page_size
            }
        })
    except Exception as e:
        logger.error('分页查询失败: %s', str(e))
        return jsonify({'status': 'error', 'message': '查询失败'}), 500