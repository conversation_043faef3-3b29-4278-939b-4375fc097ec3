from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:root@localhost/medical'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)  # 修改字段名
    phone = db.Column(db.String(20), unique=True)
    name = db.Column(db.String(10), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return '<User %r>' % self.username

class MedicalIndex(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    reference_range = db.Column(db.String(200))
    unit = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime)

@app.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    # 添加参数验证
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({'status': 'error', 'message': 'Missing username or password'}), 400
    
    user = User.query.filter_by(username=data['username']).first()
    if user and user.check_password(data['password']):  # 使用密码哈希验证
        return jsonify({'status': 'success', 'user_id': user.id})
    else:
        return jsonify({'status': 'error', 'message': 'Invalid credentials'}), 401

@app.route('/user/<int:user_id>', methods=['GET'])
def get_user(user_id):
    user = User.query.get(user_id)
    if user:
        return jsonify({'username': user.username, 'phone': user.phone})  # 修复转义字符
    else:
        return jsonify({'status': 'error', 'message': 'User not found'}), 404

@app.route('/medical_indices', methods=['GET'])
def get_medical_indices():
    indices = MedicalIndex.query.all()
    return jsonify([{
        'id': idx.id,
        'name': idx.name,
        'unit': idx.unit,
        'reference_range': idx.reference_range
    } for idx in indices])

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)