medical_app/
├── app/
│   ├── __init__.py          # 应用工厂
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   └── medical.py
│   ├── api/                 # API蓝图
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证相关
│   │   ├── user.py          # 用户相关
│   │   └── medical.py       # 医疗数据相关
│   ├── utils/               # 工具函数
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证工具
│   │   └── helpers.py       # 辅助函数
│   └── config.py            # 配置文件
├── migrations/              # 数据库迁移
├── tests/                   # 测试
├── .env                     # 环境变量
├── .gitignore
├── requirements.txt
└── run.py                   # 启动脚本